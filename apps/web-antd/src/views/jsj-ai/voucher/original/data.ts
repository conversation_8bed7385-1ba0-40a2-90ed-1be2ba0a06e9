// 发票和银行回单tab的表格字段和筛选schema定义
import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { Button } from 'ant-design-vue';

// 公司选项接口
export interface CompanyOption {
  label: string;
  value: string;
}

// 表格列配置类型
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  resizable?: boolean;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  customRender?: (params: any) => any;
}

// 通用的文本渲染函数
const renderText = (text: any) => {
  return h(
    'span',
    {
      title: text || '',
    },
    text || '-',
  );
};

// 通用的操作按钮渲染函数
const renderActionButton = (record: any, onViewFile: (record: any) => void) => {
  return h(
    Button,
    {
      onClick: () => onViewFile(record),
      size: 'small',
      type: 'link',
    },
    () => '查看原文件',
  );
};

// 发票表格列配置
export const getInvoiceColumns = (
  activeTab: 'input' | 'output',
  onViewFile: (record: any) => void,
): TableColumn[] => {
  const baseColumns: TableColumn[] = [
    {
      title: '发票号码',
      dataIndex: 'digital_invoice_number',
      key: 'digital_invoice_number',
      width: 160,
      minWidth: 120,
      maxWidth: 260,
      resizable: true,
      customRender: ({ text }) => renderText(text),
    },
    {
      title: '货物或劳务明细',
      dataIndex: 'goods_name',
      key: 'goods_name',
      width: 200,
      minWidth: 150,
      maxWidth: 350,
      resizable: true,
      customRender: ({ text }) => renderText(text),
    },
    {
      title: 'AI记账场景',
      dataIndex: 'scene',
      key: 'scene',
      width: 130,
      minWidth: 100,
      maxWidth: 200,
      resizable: true,
      customRender: ({ text }) => renderText(text),
    },
    {
      title: '凭证号',
      dataIndex: 'voucher_num',
      key: 'voucher_num',
      width: 120,
      minWidth: 80,
      maxWidth: 180,
      resizable: true,
      customRender: ({ text }) => renderText(text),
    },
  ];

  // 只在进项发票tab页显示销方名称列和税款所属期列
  if (activeTab === 'input') {
    baseColumns.push({
      title: '销方名称',
      dataIndex: 'seller_name',
      key: 'seller_name',
      width: 180,
      minWidth: 120,
      maxWidth: 250,
      resizable: true,
      customRender: ({ text }) => renderText(text),
    });

    // 添加税款所属期列
    baseColumns.push({
      title: '税款所属期',
      dataIndex: 'month',
      key: 'month',
      width: 120,
      minWidth: 100,
      maxWidth: 160,
      resizable: true,
      customRender: ({ text }) => {
        if (!text) return '-';
        // 将YYYYMM格式转换为YYYY年MM月显示
        const year = text.substring(0, 4);
        const month = text.substring(4, 6);
        return `${year}年${month}月`;
      },
    });
  }

  // 添加通用列
  const commonColumns: TableColumn[] = [
    {
      title: '开票日期',
      dataIndex: 'issue_date',
      key: 'issue_date',
      width: 120,
      minWidth: 100,
      maxWidth: 160,
      resizable: true,
    },
    {
      title: '发票类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      minWidth: 80,
      maxWidth: 180,
      resizable: true,
    },
    {
      title: '购方名称',
      dataIndex: 'buyer_name',
      key: 'buyer_name',
      width: 180,
      minWidth: 120,
      maxWidth: 250,
      resizable: true,
      customRender: ({ text }) => renderText(text),
    },
    {
      title: '价税合计',
      dataIndex: 'total',
      key: 'total',
      width: 120,
      minWidth: 80,
      maxWidth: 160,
      resizable: true,
      align: 'right' as const,
      customRender: ({ text }) => {
        return text !== undefined && text !== null
          ? `¥${Number(text).toFixed(2)}`
          : '-';
      },
    },
    {
      title: '税额',
      dataIndex: 'total_tax',
      key: 'total_tax',
      width: 100,
      minWidth: 80,
      maxWidth: 140,
      resizable: true,
      align: 'right' as const,
      customRender: ({ text }) => {
        return text !== undefined && text !== null
          ? `¥${Number(text).toFixed(2)}`
          : '-';
      },
    },
    {
      title: '税率',
      dataIndex: 'tax_rate',
      key: 'tax_rate',
      width: 80,
      minWidth: 60,
      maxWidth: 120,
      resizable: true,
      align: 'center' as const,
    },
    {
      title: '发票状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      minWidth: 80,
      maxWidth: 160,
      resizable: true,
      customRender: ({ text }) => {
        // 发票状态映射：0-正常，2-已作废，3-红冲
        const statusMap: Record<string, { color: string; text: string }> = {
          '0': { color: '#52c41a', text: '正常' },
          '2': { color: '#ff4d4f', text: '已作废' },
          '3': { color: '#fa8c16', text: '红冲' },
        };

        const status = statusMap[text] || {
          color: '#666',
          text: text || '-',
        };

        return h(
          'span',
          {
            style: {
              color: status.color,
              fontWeight: '500',
            },
          },
          status.text,
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 120,
      fixed: 'right' as const,
      customRender: ({ record }) => renderActionButton(record, onViewFile),
    },
  ];

  return [...baseColumns, ...commonColumns];
};

// 银行回单表格列配置
export const getBankReceiptColumns = (
  onViewFile: (record: any) => void,
): TableColumn[] => [
  {
    title: '回单编号',
    dataIndex: 'transaction_id',
    key: 'transaction_id',
    width: 150,
    minWidth: 100,
    maxWidth: 250,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '摘要',
    dataIndex: 'summary',
    key: 'summary',
    width: 200,
    minWidth: 120,
    maxWidth: 300,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: 'AI记账场景',
    dataIndex: 'scene',
    key: 'scene',
    width: 150,
    minWidth: 100,
    maxWidth: 200,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '凭证号',
    dataIndex: 'voucher_num',
    key: 'voucher_num',
    width: 120,
    minWidth: 80,
    maxWidth: 180,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '交易日期',
    dataIndex: 'transaction_time',
    key: 'transaction_time',
    width: 120,
    minWidth: 100,
    maxWidth: 180,
    resizable: true,
  },
  {
    title: '收支类型',
    dataIndex: 'type',
    key: 'type',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
  },
  {
    title: '对方户名称',
    dataIndex: 'counterparty_account_name',
    key: 'counterparty_account_name',
    width: 180,
    minWidth: 120,
    maxWidth: 250,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '收入金额',
    dataIndex: 'income_amount',
    key: 'income_amount',
    width: 120,
    minWidth: 80,
    maxWidth: 160,
    resizable: true,
    align: 'right' as const,
    customRender: ({ record }) => {
      // 如果是收入类型，显示金额，否则显示 -
      return h(
        'span',
        {
          style: { color: record.type === '收入' ? '#52c41a' : '#999' },
        },
        record.type === '收入' ? record.amount : '-',
      );
    },
  },
  {
    title: '支出金额',
    dataIndex: 'expense_amount',
    key: 'expense_amount',
    width: 120,
    minWidth: 80,
    maxWidth: 160,
    resizable: true,
    align: 'right' as const,
    customRender: ({ record }) => {
      // 如果是支出类型，显示金额，否则显示 -
      return h(
        'span',
        {
          style: { color: record.type === '支出' ? '#ff4d4f' : '#999' },
        },
        record.type === '支出' ? record.amount : '-',
      );
    },
  },
  {
    title: '余额',
    dataIndex: 'balance',
    key: 'balance',
    width: 120,
    minWidth: 80,
    maxWidth: 160,
    resizable: true,
    align: 'right' as const,
    customRender: ({ text }) => h('span', {}, text || '-'),
  },
  {
    title: '币种',
    dataIndex: 'currency',
    key: 'currency',
    width: 60,
    minWidth: 50,
    maxWidth: 80,
    resizable: true,
  },
  {
    title: '备注',
    dataIndex: 'note',
    key: 'note',
    width: 150,
    minWidth: 100,
    maxWidth: 250,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '附言',
    dataIndex: 'desc',
    key: 'desc',
    width: 150,
    minWidth: 100,
    maxWidth: 250,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 120,
    fixed: 'right' as const,
    customRender: ({ record }) => renderActionButton(record, onViewFile),
  },
];

// 工资单表格列配置
export const getPayrollColumns = (): TableColumn[] => [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '员工编号',
    dataIndex: 'employee_id',
    key: 'employee_id',
    width: 120,
    minWidth: 100,
    maxWidth: 180,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '身份证号',
    dataIndex: 'id_number',
    key: 'id_number',
    width: 160,
    minWidth: 140,
    maxWidth: 200,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
  {
    title: '基本工资',
    dataIndex: 'basic_salary',
    key: 'basic_salary',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
  },
  {
    title: '津贴',
    dataIndex: 'allowances',
    key: 'allowances',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
    align: 'right' as const,
  },
  {
    title: '社保扣除',
    dataIndex: 'social_insurance_deduction',
    key: 'social_insurance_deduction',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
  },
  {
    title: '公积金扣除',
    dataIndex: 'housing_fund_deduction',
    key: 'housing_fund_deduction',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
  },
  {
    title: '个人所得税',
    dataIndex: 'income_tax',
    key: 'income_tax',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
  },
  {
    title: '实发工资',
    dataIndex: 'net_salary',
    key: 'net_salary',
    width: 100,
    minWidth: 80,
    maxWidth: 150,
    resizable: true,
    align: 'right' as const,
  },
  {
    title: '工作天数',
    dataIndex: 'work_days',
    key: 'work_days',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
    align: 'center' as const,
  },
  {
    title: '加班小时',
    dataIndex: 'overtime_hours',
    key: 'overtime_hours',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
    align: 'center' as const,
  },
  {
    title: '月份',
    dataIndex: 'month',
    key: 'month',
    width: 80,
    minWidth: 60,
    maxWidth: 120,
    resizable: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 150,
    minWidth: 100,
    maxWidth: 250,
    resizable: true,
    customRender: ({ text }) => renderText(text),
  },
];

// 发票列表筛选schema
export const invoiceQuerySchema = () => [
  {
    component: 'RangePicker',
    fieldName: 'dateRange',
    label: '开票日期',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '发票状态',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '正常', value: '0' },
        { label: '已作废', value: '2' },
      ],
      allowClear: true,
      placeholder: '请选择状态',
    },
  },
];

// 银行回单表格字段
export const bankColumns: VxeGridProps['columns'] = [
  {
    title: '公司名称',
    field: 'company_name',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '交易时间',
    field: 'transaction_time',
    width: 150,
  },
  {
    title: '月份',
    field: 'month',
    width: 80,
  },
  {
    title: '类型',
    field: 'type',
    width: 80,
  },
  {
    title: '凭证编号',
    field: 'voucher_num',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '交易流水号',
    field: 'transaction_id',
    width: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '账户名称',
    field: 'account_name',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '银行名称',
    field: 'bank_name',
    width: 120,
  },
  {
    title: '对方账户名称',
    field: 'counterparty_account_name',
    width: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '金额',
    field: 'amount',
    width: 120,
    align: 'right',
  },
  {
    title: '币种',
    field: 'currency',
    width: 60,
  },
  {
    title: '摘要',
    field: 'summary',
    width: 200,
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '备注',
    field: 'note',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 工资单表格字段
export const payrollColumns: VxeGridProps['columns'] = [
  {
    title: '姓名',
    field: 'name',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '员工编号',
    field: 'employee_id',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '身份证号',
    field: 'id_number',
    width: 180,
    showOverflow: 'tooltip',
  },
  {
    title: '基本工资',
    field: 'basic_salary',
    width: 100,
    align: 'right',
  },
  {
    title: '津贴',
    field: 'allowances',
    width: 80,
    align: 'right',
  },
  {
    title: '社保扣除',
    field: 'social_insurance_deduction',
    width: 100,
    align: 'right',
  },
  {
    title: '公积金扣除',
    field: 'housing_fund_deduction',
    width: 100,
    align: 'right',
  },
  {
    title: '个人所得税',
    field: 'income_tax',
    width: 100,
    align: 'right',
  },
  {
    title: '实发工资',
    field: 'net_salary',
    width: 100,
    align: 'right',
  },
  {
    title: '工作天数',
    field: 'work_days',
    width: 80,
    align: 'center',
  },
  {
    title: '加班小时',
    field: 'overtime_hours',
    width: 80,
    align: 'center',
  },
  {
    title: '月份',
    field: 'month',
    width: 80,
  },
  {
    title: '备注',
    field: 'remark',
    width: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 工资单筛选schema
export const payrollQuerySchema = () => [
  {
    component: 'MonthPicker',
    fieldName: 'month',
    label: '月份',
    required: true,
    defaultValue: (() => {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      return `${year}${month}`;
    })(), // 当前月份，格式如202502
    componentProps: {
      placeholder: '请选择月份',
      format: 'YYYY年MM月',
      valueFormat: 'YYYYMM',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '姓名',
    componentProps: {
      placeholder: '请输入姓名',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'id_number',
    label: '身份证号',
    componentProps: {
      placeholder: '请输入身份证号',
      allowClear: true,
    },
  },
  {
    component: 'Input',
    fieldName: 'voucher_num',
    label: '凭证编号',
    componentProps: {
      placeholder: '请输入凭证编号',
      allowClear: true,
    },
  },
];

// 银行回单筛选schema
export const bankQuerySchema = (bankOptions: { label: string; value: string }[] = []) => [
  {
    component: 'Select',
    fieldName: 'bank_name',
    label: '银行名称',
    componentProps: {
      options: [{ label: '全部', value: '' }, ...bankOptions],
      allowClear: true,
      placeholder: '请选择银行名称',
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option?.label?.toLowerCase().includes(input.toLowerCase());
      },
    },
  },
  {
    component: 'RangePicker',
    fieldName: 'dateRange',
    label: '时间范围',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['开始时间', '结束时间'],
      allowClear: true,
    },
  },
  {
    component: 'Select',
    fieldName: 'type',
    label: '收支类型',
    defaultValue: '',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '收入', value: '收入' },
        { label: '支出', value: '支出' },
      ],
      allowClear: true,
      placeholder: '请选择收支类型',
    },
  },
  {
    component: 'Input',
    fieldName: 'search_text',
    label: '搜索',
    componentProps: {
      placeholder: '请输入摘要/户名/备注',
      allowClear: true,
    },
  },
];
